version: "3.9"
services:
  gateway:
    build:
      context: ./gateway
    image: zhao/gateway:1.0.0
    ports:
      - "8080:8080"
    networks:
      - zhao-network
    environment:
      - SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR=nacos-server:8848
      - JAVA_OPTS=-Dproject.name=gateway
  order-service:
    build:
      context: ./order-service
    image: zhao/order-service:1.0.0
    networks:
      - zhao-network
    environment:
      - JAVA_OPTS=-Dproject.name=order-service -Dcsp.sentinel.dashboard.server=sentinel-dashboard:8858 -Dcsp.sentinel.api.port=8719
      - SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR=nacos-server:8848
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=nacos-server:8848
      - SPRING_DATASOURCE_URL=*************************************************************************************************************************************
      - SPRING_CLOUD_SENTINEL_TRANSPORT_DASHBOARD=sentinel-dashboard:8858
      - SEATA_REGISTRY_NACOS_SERVER_ADDR=nacos-server:8848
      - SEATA_CONFIG_NACOS_SERVER_ADDR=nacos-server:8848
      - ROCKETMQ_NAMESRV_ADDR:rocketmq-namesrv:9876
  inventory-service:
    build:
      context: ./inventory-service
    image: zhao/inventory-service:1.0.0
    networks:
      - zhao-network
    environment:
      - JAVA_OPTS=-Dproject.name=inventory-service -Dcsp.sentinel.dashboard.server=sentinel-dashboard:8858 -Dcsp.sentinel.api.port=8720
      - SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR=nacos-server:8848
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=nacos-server:8848
      - SPRING_CLOUD_SENTINEL_TRANSPORT_DASHBOARD=sentinel-dashboard:8858
      - SEATA_REGISTRY_NACOS_SERVER_ADDR=nacos-server:8848
      - SEATA_CONFIG_NACOS_SERVER_ADDR=nacos-server:8848
      - ROCKETMQ_NAMESRV_ADDR:rocketmq-namesrv:9876

  account-service:
    build:
      context: ./account-service
    image: zhao/account-service:1.0.0
    networks:
      - zhao-network
    environment:
      - JAVA_OPTS=-Dproject.name=account-service -Dcsp.sentinel.dashboard.server=sentinel-dashboard:8858 -Dcsp.sentinel.api.port=8721
      - SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR=nacos-server:8848
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=nacos-server:8848
      - SPRING_CLOUD_SENTINEL_TRANSPORT_DASHBOARD=sentinel-dashboard:8858
      - SEATA_REGISTRY_NACOS_SERVER_ADDR=nacos-server:8848
      - SEATA_CONFIG_NACOS_SERVER_ADDR=nacos-server:8848
      - ROCKETMQ_NAMESRV_ADDR:rocketmq-namesrv:9876

networks:
  zhao-network:
    external: true
    name: zhao-springcloud_zhao-network
server:
  port: 8081

spring:
  application:
    name: order-service
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************
    username: root
    password: 123
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: stat,slf4j
  
  # MyBatis Plus配置
  mybatis-plus:
    mapper-locations: classpath:mapper/*.xml
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        id-type: auto
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
  
  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: nacos-server:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: nacos-server:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        import-check:
          enabled: false
    
    # OpenFeign配置
    openfeign:
      client:
        config:
          default:
            connectTimeout: 3000
            readTimeout: 5000
            loggerLevel: basic
    
    # Sentinel配置
    sentinel:
      transport:
        # 本地开发默认连接本机控制台；容器环境可通过环境变量 SPRING_CLOUD_SENTINEL_TRANSPORT_DASHBOARD 覆盖
        dashboard: sentinel-dashboard:8858
      enabled: true
      eager: true

    # RocketMQ配置
    rocketmq:
      name-server: ${ROCKETMQ_NAMESRV_ADDR:rocketmq-namesrv:9876}
      producer:
        group: ${spring.application.name}-producer
        send-message-timeout: 3000
        retry-times-when-send-failed: 2
        retry-times-when-send-async-failed: 2

# Seata配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: order-service-group
  enable-auto-data-source-proxy: false
  service:
    vgroup-mapping:
      order-service-group: default
    enable-degrade: false
    disable-global-transaction: false
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: nacos-server:8848
      group: SEATA_GROUP
      namespace: dev
      cluster: default
  config:
    type: nacos
    nacos:
      server-addr: nacos-server:8848
      namespace: dev
      group: SEATA_GROUP

# 日志配置
logging:
  level:
    com.zhao.order: debug
    com.zhao.common: debug
    org.springframework.cloud.openfeign: debug
    io.seata: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
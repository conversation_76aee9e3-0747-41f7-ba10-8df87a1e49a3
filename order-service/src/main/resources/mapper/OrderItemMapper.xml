<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhao.order.infrastructure.persistence.mapper.OrderItemMapper">

    <!-- 结果映射 -->
    <resultMap id="orderItemResultMap" type="com.zhao.order.infrastructure.persistence.po.OrderItemDO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_image" property="productImage"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="quantity" property="quantity"/>
        <result column="subtotal" property="subtotal"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 根据订单ID查询订单项列表 -->
    <select id="selectByOrderId" resultMap="orderItemResultMap">
        SELECT id, order_id, product_id, product_name, product_image, unit_price, quantity, subtotal, create_time, update_time, deleted
        FROM order_item
        WHERE order_id = #{orderId} AND deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据订单ID删除订单项 -->
    <update id="deleteByOrderId">
        UPDATE order_item
        SET deleted = 1
        WHERE order_id = #{orderId}
    </update>

    <!-- 批量插入订单项 -->
    <insert id="batchInsert">
        INSERT INTO order_item (order_id, product_id, product_name, product_image, unit_price, quantity, subtotal)
        VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.orderId}, #{item.productId}, #{item.productName}, #{item.productImage}, #{item.unitPrice}, #{item.quantity}, #{item.subtotal})
        </foreach>
    </insert>

</mapper>
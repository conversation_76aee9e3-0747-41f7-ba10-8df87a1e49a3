# 后端项目需求说明

## 一、技术选型
- JDK 17
- Spring Boot
- Spring Cloud
- Nacos（注册中心与配置中心）
- OpenFeign（服务间调用）
- Sentinel（服务容错与限流）
- Gateway（API网关）
- Seata（分布式事务）
- Mybatis
- Mybatis-Plus
- Lombok
- MySQL 8.0（关系型数据库）
- Druid（数据库连接池/数据源）
- Swagger

## 二、业务模块
1. 订单服务（order-service）
   - 负责订单的创建、查询、管理
   - 需与库存服务、账户服务进行分布式事务处理
2. 库存服务（inventory-service）
   - 管理商品库存，支持库存扣减、回滚
   - 提供库存查询接口
3. 账户服务（account-service）
   - 管理用户账户余额，支持余额扣减、回滚
   - 提供账户查询接口

## 三、系统架构
- 微服务架构，每个业务模块独立部署
- 服务注册与发现通过 Nacos 实现
- 服务间调用采用 OpenFeign
- 分布式事务通过 Seata 管理，保证订单、库存、账户操作的一致性
- 网关统一入口，路由到各业务服务，支持限流与熔断（Sentinel）
- 配置中心统一管理服务配置（Nacos）

## 四、功能流程示例
1. 用户下单：
   - 订单服务发起下单请求
   - 调用库存服务扣减库存
   - 调用账户服务扣减余额
   - Seata 管理分布式事务，确保三方操作一致性
2. 查询订单/库存/账户信息：
   - 各服务提供独立查询接口，通过网关统一访问

## 五、非功能性需求
- 服务高可用，支持自动注册与发现
- 接口限流与熔断，保障系统稳定性
- 配置热更新，便于运维管理
- 日志与监控，便于故障排查

## 六、后续扩展建议
- 可扩展更多业务模块（如支付、物流等）
- 支持多环境配置与灰度发布

## 七、DDD领域驱动设计模块结构树状图

```text
zhao-springcloud
├── zhao-dependencies    # 父 BOM（packaging=pom，仅 dependencyManagement；统一三方依赖版本）
├── zhao-parent          # 聚合父（packaging=pom；声明 modules 与 pluginManagement，导入 BOM）
├── common               # 公共模块（工具类、通用DTO、异常等）
├── order-service
│   ├── application      # 应用服务层
│   ├── domain           # 领域层（聚合、实体、值对象、领域服务、工厂、仓储接口）
│   ├── infrastructure   # 基础设施层（持久化、第三方服务、消息等）
│   └── interfaces       # 接口层（controller、feign、dto、api）
├── inventory-service
│   ├── application
│   ├── domain
│   ├── infrastructure
│   └── interfaces
├── account-service
│   ├── application
│   ├── domain
│   ├── infrastructure
│   └── interfaces
└── gateway              # 网关服务
```


## 九、依赖版本统一约定

- Spring Boot 父 POM 统一：`org.springframework.boot:spring-boot-starter-parent:3.4.8`
- 所有业务模块均通过 `zhao-parent` 继承该版本，禁止各子模块单独覆写 Spring Boot 版本。
- 若使用 `zhao-dependencies` 导入 `spring-boot-dependencies`，其版本同样需保持为 `3.4.8`，以保证依赖对齐。

## 十、规范与约定

### 10.1 MapStruct 做装配
- 目的：解耦 DTO/领域模型/持久化对象之间的转换，提升可读性与一致性。
- 约定：
  - 组件模型：`@Mapper(componentModel = "spring")`
  - 装配类命名：`*Mapper` 或 `*Assembler`，放置在 `application.assembler` 或 `infrastructure.persistence.converter`
  - Lombok 搭配使用 `lombok-mapstruct-binding`（在父 POM 统一 annotationProcessor）
- 示例：
```java
// com.zhao.order.application.assembler.OrderAssembler
@Mapper(componentModel = "spring")
public interface OrderAssembler {
    Order toDomain(CreateOrderCommand command);
    OrderResponse toResponse(Order domain);
}
```

### 10.2 错误码与统一响应结构
- 错误码命名：`{服务简称}-{模块两位}-{三位编号}`，示例：`ORD-01-001`（订单-下单-库存不足）
- 统一响应：
```json
{
  "code": "ORD-01-001",
  "message": "库存不足",
  "traceId": "${traceId}",
  "data": null
}
```
- 控制器异常处理：统一在 `interfaces.web.advice.GlobalExceptionHandler` 兜底并映射错误码。

### 10.3 配置与命名规则（Nacos）
- 命名空间：`dev` / `test` / `prod`
- Group：`DEFAULT_GROUP`（通用）或按业务 `BUSINESS_GROUP`
- DataId：`{service-name}-{profile}.yml`（示例：`order-service-dev.yml`）
- Sentinel 规则持久化：`sentinel-{service}.json` 或 `sentinel-{service}.yml`，与服务配置同命名空间/Group

### 10.4 MyBatis-Plus 规则与 BaseMapper
- 实体与表：
  - 表名小写下划线，实体使用 `@TableName("order")`
  - 公共字段：`create_time`、`update_time` 自动填充；可启用逻辑删除字段 `deleted TINYINT(1)`
- 插件：开启分页、乐观锁、性能分析等所需插件（在 `infrastructure.config.MybatisPlusConfig`）
- BaseMapper 使用：
```java
// com.zhao.order.infrastructure.persistence.mapper.OrderMapper
public interface OrderMapper extends BaseMapper<OrderDO> {}
```
- XML 与注解：鼓励简单 CRUD 走 BaseMapper/Service；复杂查询可配合 XML（放置于 `resources/mapper/*.xml`）。

### 10.5 服务间调用（OpenFeign）
- 目标：标准化服务间 HTTP 调用，降低耦合，内置重试与日志，支持熔断降级。
- 启用：在各服务启动类或配置类启用 `@EnableFeignClients`，扫描 `infrastructure.remote` 包。
- 客户端定义：
  - 使用 `@FeignClient(name = "{service-name}", contextId = "{ClientName}", path = "/api/v1/{resource}")`。
  - 方法入参/出参仅使用接口层 DTO，禁止暴露领域对象。
  - 必须提供降级实现：优先使用 `fallbackFactory`（可获取异常），统一返回 `ApiResponse.fail(...)` 并记录 WARN 日志。
- 超时与日志（默认值，可按需在各服务覆盖）：
  - `connectTimeout=3000ms`，`readTimeout=5000ms`，`loggerLevel=basic`（排查问题临时调为 `full`）。
- 配置示例（参考）：
```yaml
spring:
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 3000
            readTimeout: 5000
            loggerLevel: basic
```

### 10.6 流控与熔断（Sentinel）
- 目标：在突发流量与异常波动下保护服务稳定性，提供快速失败与友好降级。
- 接入原则：
  - 控制器或应用服务关键用例采用 `@SentinelResource` 标注，提供 `blockHandler`（限流/规则触发）与 `fallback`（业务异常降级）。
  - Feign 客户端需开启熔断能力，并提供 `fallback/fallbackFactory` 实现。
  - 统一返回：
    - 限流触发（BlockException）：返回 HTTP 429 + `ApiResponse.fail("TOO_MANY_REQUESTS")`。
    - 熔断降级：返回 HTTP 503 + `ApiResponse.fail("SERVICE_DEGRADED")`。
- 规则建议（可通过控制台或本地文件配置）：
  - 基础流控：接口级 QPS 阈值按压测结果设定（示例：`/api/v1/order/**` 100 QPS）。
  - 熔断降级：异常比例 > 50%，最小请求数 ≥ 20 时熔断 10s；RT 降级阈值按 P99 延迟设定。
  - 热点参数限流：对下单商品 `productId` 配置热点规则，保护单品爆款。
- 控制台（可选）：
  - 本地开发可接入 Sentinel Dashboard，按需持久化规则（JSON/YAML/自建存储）。

### 10.7 分布式事务（Seata - AT 模式）
- 目标：保证跨服务（订单、库存、账户）的最终一致性，优先采用 AT 模式以降低接入成本。
- 事务边界：
  - 订单服务在编排用例（如 `OrderAppService#createOrder`）方法上使用 `@GlobalTransactional(timeoutMills = 60000, name = "create-order")`。
  - 库存/账户服务在本地资源更新处使用 `@Transactional` 保证分支一致性。
- 数据与基础设施：
  - 各业务库需创建 `undo_log` 表；数据源接入 Seata 代理（DataSourceProxy）。
  - 保证本地 SQL 具备幂等与唯一约束（如订单号唯一），避免重试导致的脏写。
- 回滚与异常：
  - 全局事务默认对 `Exception` 回滚；调用外部服务失败或校验不通过需抛出受检异常并携带明确错误码。
  - 超时回滚统一记录 ERROR 日志并输出 `xid`，便于排障。
- 示例（参考）：
```java
// 订单服务 - 应用服务
@GlobalTransactional(timeoutMills = 60000, name = "create-order")
public void createOrder(CreateOrderCommand command) {
    // 1) 本地写库（订单草稿）
    // 2) 远程扣减库存（Feign 调用）
    // 3) 远程扣减账户余额（Feign 调用）
    // 4) 本地提交订单（状态变更）
}
```

## 十一、数据库与连接池规范（MySQL 8.0 + Druid）

- 数据库版本与引擎
  - MySQL 8.0（InnoDB 引擎）
  - 字符集：`utf8mb4`，排序规则：`utf8mb4_general_ci`
  - 时间：库内保存 UTC；接口按东八区返回（或前端自适应时区）。

- 命名与建表
  - 表名/字段名统一小写下划线风格；主键 `BIGINT AUTO_INCREMENT`
  - 时间字段：`create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP`、`update_time TIMESTAMP NULL`
  - 金额使用 `DECIMAL(10,2)`；严禁浮点类型存金额
  - 建议使用 Flyway 管理 DDL/变更脚本（`V{版本}__{描述}.sql`）

- 连接池（Druid）推荐参数
  - 依赖：`com.alibaba:druid-spring-boot-starter`
  - 关键参数：
    - `initialSize=5`、`minIdle=5`、`maxActive=50`、`maxWait=60000`
    - `validationQuery=SELECT 1`、`testWhileIdle=true`、`testOnBorrow=false`、`testOnReturn=false`
    - `timeBetweenEvictionRunsMillis=60000`、`minEvictableIdleTimeMillis=300000`
    - 监控：`stat,slf4j`（按需启用）

- Spring Boot 参考配置示例
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: stat,slf4j
```

- 多环境配置与密钥管理
  - 不同环境库名/账号分离；敏感信息通过 Nacos 或 Secret/KMS 管理
  - Nacos DataId 约定：`{service-name}-{profile}.yml`，例如：`order-service-dev.yml`

- 服务与数据库对应关系
  - `order-service` 使用数据库：`order`
  - `inventory-service` 使用数据库：`inventory`
  - `account-service` 使用数据库：`account`

## 十二、容器化部署与启动（Docker + Docker Compose）

- 依赖要求
  - 已安装 Docker（建议 Docker Desktop）与 Docker Compose v2
  - 本地已配置可用的 MySQL 8.0（账号：`root`，密码：`123456`），并已创建数据库 `order`、`inventory`、`account`

- 构建与启动
  1) 打包
  ```bash
  mvn -f zhao-parent/pom.xml -DskipTests clean package
  ```
  2) 启动容器
  ```bash
  docker compose up -d --build
  ```
  3) 查看状态
  ```bash
  docker compose ps
  ```

- 验证接口
  - 网关: `GET http://localhost:8080/api/v1/gateway/ping`
  - 订单: `GET http://localhost:8081/api/v1/order/ping`
  - 库存: `GET http://localhost:8082/api/v1/inventory/ping`
  - 账户: `GET http://localhost:8083/api/v1/account/ping`

- 数据库连接说明（重要）
  - 当前 Compose 未内置 MySQL，微服务容器需连接宿主机 MySQL
  - Mac/Windows（Docker Desktop）：请将 JDBC 地址改用 `host.docker.internal` 访问宿主机
    - 示例：`********************************************?...`
  - Linux：如需从容器访问宿主机，可：
    - 方案 A：在 Compose 使用 host-gateway（Docker 需要 20.10+）
    - 方案 B：改为在 Compose 中增加 MySQL 服务，并将 JDBC 指向该服务名（如 `mysql`）

- 常用运维命令
  ```bash
  # 停止并清理
  docker compose down -v
  # 重新构建与启动
  docker compose up -d --build
  # 查看日志
  docker compose logs -f order-service
  ```

- 服务与数据库对应关系
  - `order-service` 使用数据库：`order`
  - `inventory-service` 使用数据库：`inventory`
  - `account-service` 使用数据库：`account`

### order-service 模块目录与包名约定（DDD领域驱动设计）

```text
order-service
├── src
│   ├── main
│   │   ├── java
│   │   │   └── com
│   │   │       └── zhao
│   │   │           └── order
│   │   │               ├── OrderServiceApplication.java          # 应用启动类
│   │   │               ├── application                           # 应用层：编排业务用例、事务脚本、应用服务
│   │   │               │   ├── service                          # 应用服务：编排领域对象，实现业务用例
│   │   │               │   │   ├── OrderAppService.java        # 订单应用服务：创建订单、查询订单等用例
│   │   │               │   │   └── OrderQueryService.java      # 订单查询服务：复杂查询、统计等用例
│   │   │               │   ├── command                          # 命令对象：CQRS模式中的命令
│   │   │               │   │   ├── CreateOrderCommand.java     # 创建订单命令
│   │   │               │   │   ├── CancelOrderCommand.java     # 取消订单命令
│   │   │               │   │   └── ConfirmOrderCommand.java    # 确认订单命令
│   │   │               │   ├── query                            # 查询对象：CQRS模式中的查询
│   │   │               │   │   ├── OrderQuery.java             # 订单查询条件
│   │   │               │   │   └── OrderStatisticsQuery.java   # 订单统计查询
│   │   │               │   ├── assembler                        # 装配器：DTO与领域对象互转
│   │   │               │   │   ├── OrderAssembler.java         # 订单装配器
│   │   │               │   │   └── OrderItemAssembler.java     # 订单项装配器
│   │   │               │   └── event                            # 应用事件：领域事件发布
│   │   │               │       ├── OrderCreatedEvent.java      # 订单创建事件
│   │   │               │       └── OrderStatusChangedEvent.java # 订单状态变更事件
│   │   │               ├── domain                               # 领域层：核心业务逻辑、领域模型
│   │   │               │   ├── model                            # 领域模型：实体、值对象、聚合根
│   │   │               │   │   ├── aggregate                    # 聚合根
│   │   │               │   │   │   └── Order.java              # 订单聚合根
│   │   │               │   │   ├── entity                       # 实体
│   │   │               │   │   │   ├── OrderItem.java          # 订单项实体
│   │   │               │   │   │   └── OrderPayment.java       # 订单支付实体
│   │   │               │   │   ├── valueobject                 # 值对象
│   │   │               │   │   │   ├── OrderId.java            # 订单ID值对象
│   │   │               │   │   │   ├── OrderStatus.java        # 订单状态值对象
│   │   │               │   │   │   ├── Money.java              # 金额值对象
│   │   │               │   │   │   └── Address.java            # 地址值对象
│   │   │               │   │   └── event                        # 领域事件
│   │   │               │   │       ├── OrderCreatedEvent.java  # 订单创建领域事件
│   │   │               │   │       └── OrderPaidEvent.java     # 订单支付领域事件
│   │   │               │   ├── repository                       # 仓储接口：领域对象持久化抽象
│   │   │               │   │   ├── OrderRepository.java        # 订单仓储接口
│   │   │               │   │   └── OrderItemRepository.java    # 订单项仓储接口
│   │   │               │   ├── service                          # 领域服务：跨实体的业务逻辑
│   │   │               │   │   ├── OrderDomainService.java     # 订单领域服务
│   │   │               │   │   ├── OrderValidationService.java # 订单验证服务
│   │   │               │   │   └── OrderCalculationService.java # 订单计算服务
│   │   │               │   ├── factory                          # 工厂：复杂对象创建
│   │   │               │   │   ├── OrderFactory.java           # 订单工厂
│   │   │               │   │   └── OrderItemFactory.java       # 订单项工厂
│   │   │               │   └── policy                           # 策略：业务规则和策略
│   │   │               │       ├── DiscountPolicy.java         # 折扣策略
│   │   │               │       └── ShippingPolicy.java         # 配送策略
│   │   │               ├── infrastructure                       # 基础设施层：技术实现细节
│   │   │               │   ├── config                          # 配置类
│   │   │               │   │   ├── MybatisPlusConfig.java      # MyBatis-Plus配置
│   │   │               │   │   ├── FeignConfig.java            # OpenFeign配置
│   │   │               │   │   ├── SeataConfig.java            # Seata分布式事务配置
│   │   │               │   │   └── SentinelConfig.java         # Sentinel流控配置
│   │   │               │   ├── persistence                     # 持久化：数据访问实现
│   │   │               │   │   ├── mapper                      # MyBatis Mapper接口
│   │   │               │   │   │   ├── OrderMapper.java        # 订单数据访问
│   │   │               │   │   │   └── OrderItemMapper.java    # 订单项数据访问
│   │   │               │   │   ├── po                          # 持久化对象（PO）
│   │   │               │   │   │   ├── OrderDO.java            # 订单持久化对象
│   │   │               │   │   │   └── OrderItemDO.java        # 订单项持久化对象
│   │   │               │   │   ├── converter                   # PO与领域模型转换器
│   │   │               │   │   │   ├── OrderPOConverter.java   # 订单PO转换器
│   │   │               │   │   │   └── OrderItemPOConverter.java # 订单项PO转换器
│   │   │               │   │   └── repository                  # 仓储实现
│   │   │               │   │       ├── OrderRepositoryImpl.java # 订单仓储实现
│   │   │               │   │       └── OrderItemRepositoryImpl.java # 订单项仓储实现
│   │   │               │   ├── remote                           # 远程服务调用
│   │   │               │   │   ├── inventory                    # 库存服务客户端
│   │   │               │   │   │   ├── InventoryClient.java    # 库存服务Feign客户端
│   │   │               │   │   │   └── InventoryClientFallbackFactory.java # 库存服务降级工厂
│   │   │               │   │   └── account                      # 账户服务客户端
│   │   │               │   │       ├── AccountClient.java      # 账户服务Feign客户端
│   │   │               │   │       └── AccountClientFallbackFactory.java # 账户服务降级工厂
│   │   │               │   ├── messaging                        # 消息：事件发布、消息队列
│   │   │               │   │   ├── publisher                    # 事件发布器
│   │   │               │   │   │   └── OrderEventPublisher.java # 订单事件发布器
│   │   │               │   │   └── consumer                     # 事件消费者
│   │   │               │   │       └── PaymentEventConsumer.java # 支付事件消费者
│   │   │               │   └── cache                            # 缓存：本地缓存、分布式缓存
│   │   │               │       ├── OrderCacheService.java      # 订单缓存服务
│   │   │               │       └── ProductCacheService.java    # 商品缓存服务
│   │   │               └── interfaces                           # 接口层：对外暴露的API
│   │   │                   ├── web                              # Web接口：REST API
│   │   │                   │   ├── OrderController.java        # 订单控制器
│   │   │                   │   ├── OrderItemController.java    # 订单项控制器
│   │   │                   │   └── advice                       # 异常处理、参数校验
│   │   │                       ├── GlobalExceptionHandler.java  # 全局异常处理器
│   │   │                       ├── SentinelExceptionHandler.java # Sentinel异常处理器
│   │   │                       └── ValidationExceptionHandler.java # 参数校验异常处理器
│   │   │                   ├── dto                              # 数据传输对象
│   │   │                   │   ├── request                      # 请求DTO
│   │   │                   │   │   ├── CreateOrderRequest.java  # 创建订单请求
│   │   │                   │   │   ├── UpdateOrderRequest.java  # 更新订单请求
│   │   │                   │   │   └── QueryOrderRequest.java   # 查询订单请求
│   │   │                   │   └── response                     # 响应DTO
│   │   │                   │       ├── OrderResponse.java       # 订单响应
│   │   │                   │       ├── OrderDetailResponse.java # 订单详情响应
│   │   │                   │       └── OrderListResponse.java   # 订单列表响应
│   │   │                   ├── feign                            # Feign API：供其他服务调用
│   │   │                   │   ├── OrderApi.java               # 订单对外API
│   │   │                   │   └── OrderQueryApi.java          # 订单查询对外API
│   │   │                   └── facade                           # 外观模式：简化外部调用
│   │   │                       └── OrderFacade.java             # 订单外观服务
│   │   └── resources
│   │       ├── application.yml                                  # 应用配置
│   │       ├── bootstrap.yml                                    # 启动配置
│   │       ├── mapper                                           # MyBatis XML映射文件
│   │       │   ├── OrderMapper.xml                              # 订单映射文件
│   │       │   └── OrderItemMapper.xml                          # 订单项映射文件
│   │       └── db                                               # 数据库相关
│   │           └── migration                                    # 数据库迁移脚本
│   │               ├── V1__Create_Order_Tables.sql              # 创建订单表
│   │               └── V2__Add_Order_Indexes.sql                # 添加订单索引
│   └── test                                                     # 测试代码
│       ├── java
│       │   └── com
│       │       └── zhao
│       │           └── order
│       │               ├── application                          # 应用层测试
│       │               │   ├── OrderAppServiceTest.java        # 订单应用服务测试
│       │               │   └── OrderQueryServiceTest.java      # 订单查询服务测试
│       │               ├── domain                               # 领域层测试
│       │               │   ├── OrderTest.java                  # 订单实体测试
│       │               │   ├── OrderDomainServiceTest.java     # 订单领域服务测试
│       │               │   └── OrderFactoryTest.java           # 订单工厂测试
│       │               ├── infrastructure                       # 基础设施层测试
│       │               │   ├── OrderRepositoryTest.java        # 订单仓储测试
│       │               │   └── OrderPOConverterTest.java       # 订单PO转换器测试
│       │               └── interfaces                           # 接口层测试
│       │                   ├── OrderControllerTest.java        # 订单控制器测试
│       │                   └── OrderApiTest.java               # 订单API测试
│       └── resources
│           ├── application-test.yml                              # 测试环境配置
│           └── test-data.sql                                    # 测试数据
└── pom.xml                                                       # Maven配置
```

#### DDD分层架构说明

**1. 应用层（Application Layer）**
- **职责**：编排业务用例，协调领域对象，实现事务脚本
- **特点**：无业务逻辑，只负责流程编排和事务管理
- **包含**：应用服务、命令对象、查询对象、装配器、应用事件

**2. 领域层（Domain Layer）**
- **职责**：核心业务逻辑，领域模型，业务规则
- **特点**：最核心的层，包含所有业务概念和规则
- **包含**：实体、值对象、聚合根、领域服务、工厂、策略、领域事件

**3. 基础设施层（Infrastructure Layer）**
- **职责**：技术实现细节，外部系统集成
- **特点**：实现领域层定义的接口，提供技术能力
- **包含**：持久化、远程调用、消息、缓存、配置

**4. 接口层（Interfaces Layer）**
- **职责**：对外暴露API，处理用户请求
- **特点**：薄层，只负责参数转换和结果封装
- **包含**：控制器、DTO、Feign接口、外观服务

#### 包命名规范

- **包名根**：`com.zhao.order`
- **分层标识**：`application`、`domain`、`infrastructure`、`interfaces`
- **功能模块**：`model`、`service`、`repository`、`factory`、`config`等
- **具体实现**：`impl`、`converter`、`mapper`、`po`等

#### 典型包路径示例

- **应用服务**：`com.zhao.order.application.service.OrderAppService`
- **领域模型**：`com.zhao.order.domain.model.aggregate.Order`
- **值对象**：`com.zhao.order.domain.model.valueobject.OrderStatus`
- **领域服务**：`com.zhao.order.domain.service.OrderDomainService`
- **仓储接口**：`com.zhao.order.domain.repository.OrderRepository`
- **仓储实现**：`com.zhao.order.infrastructure.persistence.repository.OrderRepositoryImpl`
- **PO转换器**：`com.zhao.order.infrastructure.persistence.converter.OrderPOConverter`
- **远程客户端**：`com.zhao.order.infrastructure.remote.inventory.InventoryClient`
- **Web控制器**：`com.zhao.order.interfaces.web.OrderController`
- **DTO对象**：`com.zhao.order.interfaces.dto.request.CreateOrderRequest`
- **Feign接口**：`com.zhao.order.interfaces.feign.OrderApi`

### inventory-service 模块目录与包名约定（DDD领域驱动设计）

```text
inventory-service
├── src
│   ├── main
│   │   ├── java
│   │   │   └── com
│   │   │       └── zhao
│   │   │           └── inventory
│   │   │               ├── InventoryServiceApplication.java      # 应用启动类
│   │   │               ├── application                           # 应用层：编排业务用例、事务脚本、应用服务
│   │   │               │   ├── service                          # 应用服务：编排领域对象，实现业务用例
│   │   │               │   │   ├── InventoryAppService.java     # 库存应用服务：库存扣减、回滚、查询等用例
│   │   │               │   │   ├── InventoryQueryService.java   # 库存查询服务：库存统计、预警等用例
│   │   │               │   │   └── InventorySyncService.java    # 库存同步服务：与商品服务同步等用例
│   │   │               │   ├── command                          # 命令对象：CQRS模式中的命令
│   │   │               │   │   ├── DeductInventoryCommand.java  # 扣减库存命令
│   │   │               │   │   ├── RestoreInventoryCommand.java # 恢复库存命令
│   │   │               │   │   ├── AdjustInventoryCommand.java  # 调整库存命令
│   │   │               │   │   └── ReserveInventoryCommand.java # 预占库存命令
│   │   │               │   ├── query                            # 查询对象：CQRS模式中的查询
│   │   │               │   │   ├── InventoryQuery.java          # 库存查询条件
│   │   │               │   │   ├── InventoryStatisticsQuery.java # 库存统计查询
│   │   │               │   │   └── InventoryAlertQuery.java     # 库存预警查询
│   │   │               │   ├── assembler                        # 装配器：DTO与领域对象互转
│   │   │               │   │   ├── InventoryAssembler.java      # 库存装配器
│   │   │               │   │   ├── ProductAssembler.java        # 商品装配器
│   │   │               │   │   └── WarehouseAssembler.java      # 仓库装配器
│   │   │               │   └── event                            # 应用事件：领域事件发布
│   │   │               │       ├── InventoryDeductedEvent.java  # 库存扣减事件
│   │   │               │       ├── InventoryRestoredEvent.java  # 库存恢复事件
│   │   │               │       └── InventoryAlertEvent.java     # 库存预警事件
│   │   │               ├── domain                               # 领域层：核心业务逻辑、领域模型
│   │   │               │   ├── model                            # 领域模型：实体、值对象、聚合根
│   │   │               │   │   ├── aggregate                    # 聚合根
│   │   │               │   │   │   ├── Inventory.java           # 库存聚合根
│   │   │               │   │   │   └── Product.java             # 商品聚合根
│   │   │               │   │   ├── entity                       # 实体
│   │   │               │   │   │   ├── InventoryItem.java       # 库存项实体
│   │   │               │   │   │   ├── InventoryTransaction.java # 库存事务实体
│   │   │               │   │   │   ├── Warehouse.java           # 仓库实体
│   │   │               │   │   │   └── ProductCategory.java     # 商品分类实体
│   │   │               │   │   ├── valueobject                  # 值对象
│   │   │               │   │   │   ├── InventoryId.java         # 库存ID值对象
│   │   │               │   │   │   ├── ProductId.java           # 商品ID值对象
│   │   │               │   │   │   ├── WarehouseId.java         # 仓库ID值对象
│   │   │               │   │   │   ├── Quantity.java            # 数量值对象
│   │   │               │   │   │   ├── InventoryStatus.java     # 库存状态值对象
│   │   │               │   │   │   ├── Unit.java                # 单位值对象
│   │   │               │   │   │   └── Location.java            # 库位值对象
│   │   │               │   │   └── event                        # 领域事件
│   │   │               │   │       ├── InventoryDeductedEvent.java # 库存扣减领域事件
│   │   │               │   │       ├── InventoryRestoredEvent.java # 库存恢复领域事件
│   │   │               │   │       ├── LowStockAlertEvent.java  # 低库存预警领域事件
│   │   │               │   │       └── OutOfStockEvent.java     # 缺货领域事件
│   │   │               │   ├── repository                       # 仓储接口：领域对象持久化抽象
│   │   │               │   │   ├── InventoryRepository.java     # 库存仓储接口
│   │   │               │   │   ├── ProductRepository.java       # 商品仓储接口
│   │   │               │   │   ├── WarehouseRepository.java     # 仓库仓储接口
│   │   │               │   │   └── InventoryTransactionRepository.java # 库存事务仓储接口
│   │   │               │   ├── service                          # 领域服务：跨实体的业务逻辑
│   │   │               │   │   ├── InventoryDomainService.java  # 库存领域服务
│   │   │               │   │   ├── InventoryValidationService.java # 库存验证服务
│   │   │               │   │   ├── InventoryCalculationService.java # 库存计算服务
│   │   │               │   │   ├── StockAlertService.java       # 库存预警服务
│   │   │               │   │   └── InventoryOptimizationService.java # 库存优化服务
│   │   │               │   ├── factory                          # 工厂：复杂对象创建
│   │   │               │   │   ├── InventoryFactory.java        # 库存工厂
│   │   │               │   │   ├── ProductFactory.java          # 商品工厂
│   │   │               │   │   └── WarehouseFactory.java        # 仓库工厂
│   │   │               │   └── policy                           # 策略：业务规则和策略
│   │   │               │       ├── SafetyStockPolicy.java       # 安全库存策略
│   │   │               │       ├── ReorderPointPolicy.java      # 补货点策略
│   │   │               │       ├── ABCAnalysisPolicy.java      # ABC分析策略
│   │   │               │       └── FIFOLIFOPolicy.java          # 先进先出/后进先出策略
│   │   │               ├── infrastructure                       # 基础设施层：技术实现细节
│   │   │               │   ├── config                          # 配置类
│   │   │               │   │   ├── MybatisPlusConfig.java       # MyBatis-Plus配置
│   │   │               │   │   ├── SeataConfig.java             # Seata分布式事务配置
│   │   │               │   │   ├── SentinelConfig.java          # Sentinel流控配置
│   │   │               │   │   ├── CacheConfig.java             # 缓存配置
│   │   │               │   │   └── AsyncConfig.java             # 异步配置
│   │   │               │   ├── persistence                     # 持久化：数据访问实现
│   │   │               │   │   ├── mapper                      # MyBatis Mapper接口
│   │   │               │   │   │   ├── InventoryMapper.java     # 库存数据访问
│   │   │               │   │   │   ├── ProductMapper.java       # 商品数据访问
│   │   │               │   │   │   ├── WarehouseMapper.java     # 仓库数据访问
│   │   │               │   │   │   └── InventoryTransactionMapper.java # 库存事务数据访问
│   │   │               │   │   ├── po                          # 持久化对象（PO）
│   │   │               │   │   │   ├── InventoryDO.java         # 库存持久化对象
│   │   │ │   │   │   ├── ProductDO.java         # 商品持久化对象
│   │   │   │   │   │   ├── WarehouseDO.java     # 仓库持久化对象
│   │   │   │   │   │   └── InventoryTransactionDO.java # 库存事务持久化对象
│   │   │   │   │   ├── converter                   # PO与领域模型转换器
│   │   │   │   │   │   ├── InventoryPOConverter.java # 库存PO转换器
│   │   │   │   │   │   ├── ProductPOConverter.java   # 商品PO转换器
│   │   │   │   │   │   └── WarehousePOConverter.java # 仓库PO转换器
│   │   │   │   │   └── repository                  # 仓储实现
│   │   │   │   │       ├── InventoryRepositoryImpl.java # 库存仓储实现
│   │   │   │   │       ├── ProductRepositoryImpl.java   # 商品仓储实现
│   │   │   │   │       └── WarehouseRepositoryImpl.java # 仓库仓储实现
│   │   │   │   ├── remote                           # 远程服务调用
│   │   │   │   │   ├── product                      # 商品服务客户端
│   │   │   │   │   │   ├── ProductClient.java      # 商品服务Feign客户端
│   │   │   │   │   │   └── ProductClientFallbackFactory.java # 商品服务降级工厂
│   │   │   │   │   └── notification                 # 通知服务客户端
│   │   │   │   │       ├── NotificationClient.java # 通知服务Feign客户端
│   │   │   │   │       └── NotificationClientFallbackFactory.java # 通知服务降级工厂
│   │   │   │   ├── messaging                        # 消息：事件发布、消息队列
│   │   │   │   │   ├── publisher                    # 事件发布器
│   │   │   │   │   │   ├── InventoryEventPublisher.java # 库存事件发布器
│   │   │   │   │   │   └── AlertEventPublisher.java # 预警事件发布器
│   │   │   │   │   └── consumer                     # 事件消费者
│   │   │   │   │       ├── OrderEventConsumer.java # 订单事件消费者
│   │   │   │   │       └── ProductEventConsumer.java # 商品事件消费者
│   │   │   │   ├── cache                            # 缓存：本地缓存、分布式缓存
│   │   │   │   │   ├── InventoryCacheService.java  # 库存缓存服务
│   │   │   │   │   ├── ProductCacheService.java    # 商品缓存服务
│   │   │   │   │   └── WarehouseCacheService.java  # 仓库缓存服务
│   │   │   │   └── scheduler                        # 定时任务：库存检查、预警等
│   │   │   │       ├── InventoryCheckScheduler.java # 库存检查定时任务
│   │   │   │       ├── StockAlertScheduler.java     # 库存预警定时任务
│   │   │   │       └── InventoryReportScheduler.java # 库存报表定时任务
│   │   │   └── interfaces                           # 接口层：对外暴露的API
│   │   │       ├── web                              # Web接口：REST API
│   │   │       │   ├── InventoryController.java     # 库存控制器
│   │   │       │   ├── ProductController.java       # 商品控制器
│   │   │       │   ├── WarehouseController.java     # 仓库控制器
│   │   │       │   └── advice                       # 异常处理、参数校验
│   │   │           ├── GlobalExceptionHandler.java  # 全局异常处理器
│   │   │           ├── SentinelExceptionHandler.java # Sentinel异常处理器
│   │   │           └── ValidationExceptionHandler.java # 参数校验异常处理器
│   │   │       ├── dto                              # 数据传输对象
│   │   │       │   ├── request                      # 请求DTO
│   │   │       │   │   ├── DeductInventoryRequest.java # 扣减库存请求
│   │   │       │   │   ├── RestoreInventoryRequest.java # 恢复库存请求
│   │   │       │   │   ├── QueryInventoryRequest.java  # 查询库存请求
│   │   │       │   │   └── AdjustInventoryRequest.java # 调整库存请求
│   │   │       │   └── response                     # 响应DTO
│   │   │           ├── InventoryResponse.java        # 库存响应
│   │   │           ├── InventoryDetailResponse.java  # 库存详情响应
│   │   │           ├── InventoryListResponse.java    # 库存列表响应
│   │   │           └── InventoryStatisticsResponse.java # 库存统计响应
│   │   │       ├── feign                            # Feign API：供其他服务调用
│   │   │       │   ├── InventoryApi.java            # 库存对外API
│   │   │       │   ├── InventoryQueryApi.java       # 库存查询对外API
│   │   │       │   └── InventoryAlertApi.java       # 库存预警对外API
│   │   │       └── facade                           # 外观模式：简化外部调用
│   │   │           ├── InventoryFacade.java          # 库存外观服务
│   │   │           └── InventoryQueryFacade.java     # 库存查询外观服务
│   │   └── resources
│   │       ├── application.yml                      # 应用配置
│   │       ├── bootstrap.yml                        # 启动配置
│   │       ├── mapper                               # MyBatis XML映射文件
│   │       │   ├── InventoryMapper.xml              # 库存映射文件
│   │       │   ├── ProductMapper.xml                # 商品映射文件
│   │       │   └── WarehouseMapper.xml              # 仓库映射文件
│   │       └── db                                   # 数据库相关
│   │           └── migration                        # 数据库迁移脚本
│   │               ├── V1__Create_Inventory_Tables.sql # 创建库存表
│   │               ├── V2__Create_Product_Tables.sql   # 创建商品表
│   │               ├── V3__Create_Warehouse_Tables.sql # 创建仓库表
│   │               └── V4__Add_Inventory_Indexes.sql  # 添加库存索引
│   └── test                                         # 测试代码
│       ├── java
│       │   └── com
│       │       └── zhao
│       │           └── inventory
│       │               ├── application                          # 应用层测试
│       │               │   ├── InventoryAppServiceTest.java    # 库存应用服务测试
│       │               │   ├── InventoryQueryServiceTest.java  # 库存查询服务测试
│       │               │   └── InventorySyncServiceTest.java   # 库存同步服务测试
│       │               ├── domain                               # 领域层测试
│       │               │   ├── InventoryTest.java              # 库存实体测试
│       │               │   ├── InventoryDomainServiceTest.java # 库存领域服务测试
│       │               │   ├── InventoryFactoryTest.java       # 库存工厂测试
│       │               │   └── SafetyStockPolicyTest.java      # 安全库存策略测试
│       │               ├── infrastructure                       # 基础设施层测试
│       │               │   ├── InventoryRepositoryTest.java    # 库存仓储测试
│       │               │   ├── InventoryPOConverterTest.java   # 库存PO转换器测试
│       │               │   └── InventoryCacheServiceTest.java  # 库存缓存服务测试
│       │               └── interfaces                           # 接口层测试
│       │                   ├── InventoryControllerTest.java    # 库存控制器测试
│       │                   └── InventoryApiTest.java           # 库存API测试
│       └── resources
│           ├── application-test.yml                              # 测试环境配置
│           └── test-data.sql                                    # 测试数据
└── pom.xml                                                       # Maven配置
```

#### inventory-service DDD分层架构说明

**1. 应用层（Application Layer）**
- **职责**：编排库存管理用例，协调领域对象，实现事务脚本
- **特点**：无业务逻辑，只负责流程编排和事务管理
- **包含**：应用服务、命令对象、查询对象、装配器、应用事件

**2. 领域层（Domain Layer）**
- **职责**：核心库存业务逻辑，领域模型，业务规则
- **特点**：最核心的层，包含所有库存管理概念和规则
- **包含**：实体、值对象、聚合根、领域服务、工厂、策略、领域事件

**3. 基础设施层（Infrastructure Layer）**
- **职责**：技术实现细节，外部系统集成，定时任务
- **特点**：实现领域层定义的接口，提供技术能力
- **包含**：持久化、远程调用、消息、缓存、定时任务、配置

**4. 接口层（Interfaces Layer）**
- **职责**：对外暴露API，处理用户请求
- **特点**：薄层，只负责参数转换和结果封装
- **包含**：控制器、DTO、Feign接口、外观服务

#### inventory-service 包命名规范

- **包名根**：`com.zhao.inventory`
- **分层标识**：`application`、`domain`、`infrastructure`、`interfaces`
- **功能模块**：`model`、`service`、`repository`、`factory`、`config`等
- **具体实现**：`impl`、`converter`、`mapper`、`po`等

#### inventory-service 典型包路径示例

- **应用服务**：`com.zhao.inventory.application.service.InventoryAppService`
- **领域模型**：`com.zhao.inventory.domain.model.aggregate.Inventory`
- **值对象**：`com.zhao.inventory.domain.model.valueobject.Quantity`
- **领域服务**：`com.zhao.inventory.domain.service.InventoryDomainService`
- **仓储接口**：`com.zhao.inventory.domain.repository.InventoryRepository`
- **仓储实现**：`com.zhao.inventory.infrastructure.persistence.repository.InventoryRepositoryImpl`
- **PO转换器**：`com.zhao.inventory.infrastructure.persistence.converter.InventoryPOConverter`
- **远程客户端**：`com.zhao.inventory.infrastructure.remote.product.ProductClient`
- **Web控制器**：`com.zhao.inventory.interfaces.web.InventoryController`
- **DTO对象**：`com.zhao.inventory.interfaces.dto.request.DeductInventoryRequest`
- **Feign接口**：`com.zhao.inventory.interfaces.feign.InventoryApi`

## account-service 模块目录与包名约定（DDD领域驱动设计）

```text
account-service
├── src
│   ├── main
│   │   ├── java
│   │   │   └── com
│   │   │       └── zhao
│   │   │           └── account
│   │   │               ├── AccountServiceApplication.java        # 应用启动类
│   │   │               ├── application                           # 应用层：编排业务用例、事务脚本、应用服务
│   │   │               │   ├── service                          # 应用服务：编排领域对象，实现业务用例
│   │   │               │   │   ├── AccountAppService.java       # 账户应用服务：账户开户、销户、查询等用例
│   │   │               │   │   ├── AccountQueryService.java     # 账户查询服务：账户统计、流水查询等用例
│   │   │               │   │   ├── TransactionAppService.java   # 交易应用服务：转账、充值、提现等用例
│   │   │               │   │   └── AccountSyncService.java      # 账户同步服务：与用户服务同步等用例
│   │   │               │   ├── command                          # 命令对象：CQRS模式中的命令
│   │   │               │   │   ├── CreateAccountCommand.java    # 创建账户命令
│   │   │               │   │   ├── CloseAccountCommand.java     # 关闭账户命令
│   │   │               │   │   ├── DebitAccountCommand.java     # 扣减账户余额命令
│   │   │               │   │   ├── CreditAccountCommand.java    # 增加账户余额命令
│   │   │               │   │   ├── TransferCommand.java         # 转账命令
│   │   │               │   │   └── FreezeAccountCommand.java    # 冻结账户命令
│   │   │               │   ├── query                            # 查询对象：CQRS模式中的查询
│   │   │               │   │   ├── AccountQuery.java            # 账户查询条件
│   │   │               │   │   ├── TransactionQuery.java        # 交易查询条件
│   │   │               │   │   ├── AccountStatisticsQuery.java  # 账户统计查询
│   │   │               │   │   └── BalanceQuery.java            # 余额查询条件
│   │   │               │   ├── assembler                        # 装配器：DTO与领域对象互转
│   │   │               │   │   ├── AccountAssembler.java        # 账户装配器
│   │   │               │   │   ├── TransactionAssembler.java    # 交易装配器
│   │   │               │   │   └── UserAssembler.java           # 用户装配器
│   │   │               │   └── event                            # 应用事件：领域事件发布
│   │   │               │       ├── AccountCreatedEvent.java     # 账户创建事件
│   │   │               │       ├── AccountClosedEvent.java      # 账户关闭事件
│   │   │               │       ├── BalanceChangedEvent.java     # 余额变更事件
│   │   │               │       └── AccountFrozenEvent.java      # 账户冻结事件
│   │   │               ├── domain                               # 领域层：核心业务逻辑、领域模型
│   │   │               │   ├── model                            # 领域模型：实体、值对象、聚合根
│   │   │               │   │   ├── aggregate                    # 聚合根
│   │   │               │   │   │   ├── Account.java             # 账户聚合根
│   │   │               │   │   │   └── Transaction.java         # 交易聚合根
│   │   │               │   │   ├── entity                       # 实体
│   │   │               │   │   │   ├── AccountDetail.java       # 账户详情实体
│   │   │               │   │   │   ├── TransactionRecord.java   # 交易记录实体
│   │   │               │   │   │   ├── AccountStatus.java       # 账户状态实体
│   │   │               │   │   │   └── AccountType.java         # 账户类型实体
│   │   │               │   │   ├── valueobject                  # 值对象
│   │   │               │   │   │   ├── AccountId.java           # 账户ID值对象
│   │   │               │   │   │   ├── UserId.java              # 用户ID值对象
│   │   │               │   │   │   ├── Money.java               # 金额值对象
│   │   │               │   │   │   ├── Currency.java            # 货币值对象
│   │   │               │   │   │   ├── AccountNumber.java       # 账户号码值对象
│   │   │               │   │   │   ├── TransactionId.java       # 交易ID值对象
│   │   │               │   │   │   └── TransactionType.java     # 交易类型值对象
│   │   │               │   │   └── event                        # 领域事件
│   │   │               │   │       ├── AccountCreatedEvent.java # 账户创建领域事件
│   │   │               │   │       ├── BalanceChangedEvent.java # 余额变更领域事件
│   │   │               │   │       ├── AccountFrozenEvent.java  # 账户冻结领域事件
│   │   │               │   │       ├── InsufficientBalanceEvent.java # 余额不足领域事件
│   │   │               │   │       └── TransactionCompletedEvent.java # 交易完成领域事件
│   │   │               │   ├── repository                       # 仓储接口：领域对象持久化抽象
│   │   │               │   │   ├── AccountRepository.java       # 账户仓储接口
│   │   │               │   │   ├── TransactionRepository.java   # 交易仓储接口
│   │   │               │   │   └── AccountDetailRepository.java # 账户详情仓储接口
│   │   │               │   ├── service                          # 领域服务：跨实体的业务逻辑
│   │   │               │   │   ├── AccountDomainService.java    # 账户领域服务
│   │   │               │   │   ├── AccountValidationService.java # 账户验证服务
│   │   │               │   │   ├── BalanceCalculationService.java # 余额计算服务
│   │   │               │   │   ├── TransactionDomainService.java # 交易领域服务
│   │   │               │   │   ├── RiskControlService.java      # 风控服务
│   │   │               │   │   └── AccountSecurityService.java  # 账户安全服务
│   │   │               │   ├── factory                          # 工厂：复杂对象创建
│   │   │               │   │   ├── AccountFactory.java          # 账户工厂
│   │   │               │   │   ├── TransactionFactory.java      # 交易工厂
│   │   │               │   │   └── AccountNumberFactory.java    # 账户号码工厂
│   │   │               │   └── policy                           # 策略：业务规则和策略
│   │   │               │       ├── BalanceCheckPolicy.java      # 余额检查策略
│   │   │               │       ├── TransactionLimitPolicy.java  # 交易限额策略
│   │   │               │       ├── RiskControlPolicy.java       # 风控策略
│   │   │               │       ├── FeeCalculationPolicy.java    # 手续费计算策略
│   │   │               │       └── InterestCalculationPolicy.java # 利息计算策略
│   │   │               ├── infrastructure                       # 基础设施层：技术实现细节
│   │   │               │   ├── config                          # 配置类
│   │   │               │   │   ├── MybatisPlusConfig.java       # MyBatis-Plus配置
│   │   │               │   │   ├── SeataConfig.java             # Seata分布式事务配置
│   │   │               │   │   ├── SentinelConfig.java          # Sentinel流控配置
│   │   │               │   │   ├── CacheConfig.java             # 缓存配置
│   │   │               │   │   ├── AsyncConfig.java             # 异步配置
│   │   │               │   │   └── SecurityConfig.java          # 安全配置
│   │   │               │   ├── persistence                     # 持久化：数据访问实现
│   │   │               │   │   ├── mapper                      # MyBatis Mapper接口
│   │   │               │   │   │   ├── AccountMapper.java       # 账户数据访问
│   │   │               │   │   │   ├── TransactionMapper.java   # 交易数据访问
│   │   │               │   │   │   ├── AccountDetailMapper.java # 账户详情数据访问
│   │   │               │   │   │   └── AccountStatusMapper.java # 账户状态数据访问
│   │   │               │   │   ├── po                          # 持久化对象（PO）
│   │   │               │   │   │   ├── AccountDO.java           # 账户持久化对象
│   │   │               │   │   │   ├── TransactionDO.java       # 交易持久化对象
│   │   │               │   │   │   ├── AccountDetailDO.java     # 账户详情持久化对象
│   │   │               │   │   │   └── AccountStatusDO.java     # 账户状态持久化对象
│   │   │               │   │   ├── converter                   # PO与领域模型转换器
│   │   │               │   │   │   ├── AccountPOConverter.java  # 账户PO转换器
│   │   │               │   │   │   ├── TransactionPOConverter.java # 交易PO转换器
│   │   │               │   │   │   └── AccountDetailPOConverter.java # 账户详情PO转换器
│   │   │               │   │   └── repository                  # 仓储实现
│   │   │               │   │       ├── AccountRepositoryImpl.java # 账户仓储实现
│   │   │               │   │       ├── TransactionRepositoryImpl.java # 交易仓储实现
│   │   │               │   │       └── AccountDetailRepositoryImpl.java # 账户详情仓储实现
│   │   │               │   ├── remote                           # 远程服务调用
│   │   │               │   │   ├── user                         # 用户服务客户端
│   │   │               │   │   │   ├── UserClient.java          # 用户服务Feign客户端
│   │   │               │   │   │   └── UserClientFallbackFactory.java # 用户服务降级工厂
│   │   │               │   │   ├── notification                 # 通知服务客户端
│   │   │               │   │   │   ├── NotificationClient.java  # 通知服务Feign客户端
│   │   │               │   │   │   └── NotificationClientFallbackFactory.java # 通知服务降级工厂
│   │   │               │   │   └── risk                         # 风控服务客户端
│   │   │               │   │       ├── RiskClient.java          # 风控服务Feign客户端
│   │   │               │   │       └── RiskClientFallbackFactory.java # 风控服务降级工厂
│   │   │               │   ├── messaging                        # 消息：事件发布、消息队列
│   │   │               │   │   ├── publisher                    # 事件发布器
│   │   │               │   │   │   ├── AccountEventPublisher.java # 账户事件发布器
│   │   │               │   │   │   └── TransactionEventPublisher.java # 交易事件发布器
│   │   │               │   │   └── consumer                     # 事件消费者
│   │   │               │   │       ├── UserEventConsumer.java   # 用户事件消费者
│   │   │               │   │       ├── OrderEventConsumer.java  # 订单事件消费者
│   │   │               │   │       └── RiskEventConsumer.java   # 风控事件消费者
│   │   │               │   ├── cache                            # 缓存：本地缓存、分布式缓存
│   │   │               │   │   ├── AccountCacheService.java     # 账户缓存服务
│   │   │               │   │   ├── TransactionCacheService.java # 交易缓存服务
│   │   │               │   │   └── UserCacheService.java        # 用户缓存服务
│   │   │               │   ├── scheduler                        # 定时任务：账户检查、对账等
│   │   │               │   │   ├── AccountCheckScheduler.java   # 账户检查定时任务
│   │   │               │   │   ├── BalanceReconciliationScheduler.java # 余额对账定时任务
│   │   │               │   │   ├── InterestCalculationScheduler.java # 利息计算定时任务
│   │   │               │   │   └── RiskReportScheduler.java     # 风控报告定时任务
│   │   │               │   └── security                         # 安全：加密、签名、审计
│   │   │               │       ├── EncryptionService.java       # 加密服务
│   │   │               │       ├── SignatureService.java        # 签名服务
│   │   │               │       └── AuditLogService.java         # 审计日志服务
│   │   │               └── interfaces                           # 接口层：对外暴露的API
│   │   │                   ├── web                              # Web接口：REST API
│   │   │                   │   ├── AccountController.java       # 账户控制器
│   │   │                   │   ├── TransactionController.java   # 交易控制器
│   │   │                   │   ├── AccountDetailController.java # 账户详情控制器
│   │   │                   │   └── advice                       # 异常处理、参数校验
│   │   │                       ├── GlobalExceptionHandler.java  # 全局异常处理器
│   │   │                       ├── SentinelExceptionHandler.java # Sentinel异常处理器
│   │   │                       ├── ValidationExceptionHandler.java # 参数校验异常处理器
│   │   │                       └── SecurityExceptionHandler.java # 安全异常处理器
│   │   │                   ├── dto                              # 数据传输对象
│   │   │                   │   ├── request                      # 请求DTO
│   │   │                   │   │   ├── CreateAccountRequest.java # 创建账户请求
│   │   │                   │   │   ├── DebitAccountRequest.java # 扣减账户余额请求
│   │   │                   │   │   ├── CreditAccountRequest.java # 增加账户余额请求
│   │   │                   │   │   ├── TransferRequest.java     # 转账请求
│   │   │                   │   │   └── QueryAccountRequest.java  # 查询账户请求
│   │   │                   │   └── response                     # 响应DTO
│   │   │                   │       ├── AccountResponse.java      # 账户响应
│   │   │                   │       ├── AccountDetailResponse.java # 账户详情响应
│   │   │                   │       ├── TransactionResponse.java  # 交易响应
│   │   │                   │       ├── BalanceResponse.java      # 余额响应
│   │   │                   │       └── AccountListResponse.java  # 账户列表响应
│   │   │                   ├── feign                            # Feign API：供其他服务调用
│   │   │                   │   ├── AccountApi.java              # 账户对外API
│   │   │                   │   ├── AccountQueryApi.java         # 账户查询对外API
│   │   │                   │   └── TransactionApi.java          # 交易对外API
│   │   │                   └── facade                           # 外观模式：简化外部调用
│   │   │                       ├── AccountFacade.java            # 账户外观服务
│   │   │                       ├── TransactionFacade.java        # 交易外观服务
│   │   │                       └── BalanceFacade.java            # 余额外观服务
│   │   └── resources
│   │       ├── application.yml                                   # 应用配置
│   │       ├── bootstrap.yml                                     # 启动配置
│   │       ├── mapper                                            # MyBatis XML映射文件
│   │       │   ├── AccountMapper.xml                             # 账户映射文件
│   │       │   ├── TransactionMapper.xml                         # 交易映射文件
│   │       │   └── AccountDetailMapper.xml                       # 账户详情映射文件
│   │       └── db                                                # 数据库相关
│   │           └── migration                                     # 数据库迁移脚本
│   │               ├── V1__Create_Account_Tables.sql             # 创建账户表
│   │               ├── V2__Create_Transaction_Tables.sql         # 创建交易表
│   │               ├── V3__Create_Account_Detail_Tables.sql      # 创建账户详情表
│   │               └── V4__Add_Account_Indexes.sql               # 添加账户索引
│   └── test                                                      # 测试代码
│       ├── java
│       │   └── com
│       │       └── zhao
│       │           └── account
│       │               ├── application                           # 应用层测试
│       │               │   ├── AccountAppServiceTest.java       # 账户应用服务测试
│       │               │   ├── AccountQueryServiceTest.java     # 账户查询服务测试
│       │               │   └── TransactionAppServiceTest.java   # 交易应用服务测试
│       │               ├── domain                                # 领域层测试
│       │               │   ├── AccountTest.java                 # 账户实体测试
│       │               │   ├── TransactionTest.java             # 交易实体测试
│       │               │   ├── AccountDomainServiceTest.java    # 账户领域服务测试
│       │               │   ├── BalanceCalculationServiceTest.java # 余额计算服务测试
│       │               │   └── RiskControlServiceTest.java      # 风控服务测试
│       │               ├── infrastructure                        # 基础设施层测试
│       │               │   ├── AccountRepositoryTest.java       # 账户仓储测试
│       │               │   ├── AccountPOConverterTest.java      # 账户PO转换器测试
│       │               │   ├── AccountCacheServiceTest.java     # 账户缓存服务测试
│       │               │   └── EncryptionServiceTest.java       # 加密服务测试
│       │               └── interfaces                            # 接口层测试
│       │                   ├── AccountControllerTest.java       # 账户控制器测试
│       │                   └── AccountApiTest.java              # 账户API测试
│       └── resources
│           ├── application-test.yml                               # 测试环境配置
│           └── test-data.sql                                     # 测试数据
└── pom.xml                                                        # Maven配置
```

#### account-service DDD分层架构说明

**1. 应用层（Application Layer）**
- **职责**：编排账户管理用例，协调领域对象，实现事务脚本
- **特点**：无业务逻辑，只负责流程编排和事务管理
- **包含**：应用服务、命令对象、查询对象、装配器、应用事件

**2. 领域层（Domain Layer）**
- **职责**：核心账户业务逻辑，领域模型，业务规则
- **特点**：最核心的层，包含所有账户管理概念和规则
- **包含**：实体、值对象、聚合根、领域服务、工厂、策略、领域事件

**3. 基础设施层（Infrastructure Layer）**
- **职责**：技术实现细节，外部系统集成，安全、定时任务
- **特点**：实现领域层定义的接口，提供技术能力
- **包含**：持久化、远程调用、消息、缓存、定时任务、安全、配置

**4. 接口层（Interfaces Layer）**
- **职责**：对外暴露API，处理用户请求
- **特点**：薄层，只负责参数转换和结果封装
- **包含**：控制器、DTO、Feign接口、外观服务

#### account-service 包命名规范

- **包名根**：`com.zhao.account`
- **分层标识**：`application`、`domain`、`infrastructure`、`interfaces`
- **功能模块**：`model`、`service`、`repository`、`factory`、`config`等
- **具体实现**：`impl`、`converter`、`mapper`、`po`等

#### account-service 典型包路径示例

- **应用服务**：`com.zhao.account.application.service.AccountAppService`
- **领域模型**：`com.zhao.account.domain.model.aggregate.Account`
- **值对象**：`com.zhao.account.domain.model.valueobject.Money`
- **领域服务**：`com.zhao.account.domain.service.AccountDomainService`
- **仓储接口**：`com.zhao.account.domain.repository.AccountRepository`
- **仓储实现**：`com.zhao.account.infrastructure.persistence.repository.AccountRepositoryImpl`
- **PO转换器**：`com.zhao.account.infrastructure.persistence.converter.AccountPOConverter`
- **远程客户端**：`com.zhao.account.infrastructure.remote.user.UserClient`
- **Web控制器**：`com.zhao.account.interfaces.web.AccountController`
- **DTO对象**：`com.zhao.account.interfaces.dto.request.CreateAccountRequest`
- **Feign接口**：`com.zhao.account.interfaces.feign.AccountApi`

## 八、核心业务表结构设计

```
```
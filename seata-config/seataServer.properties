# SeataÃ¦ÂÂÃ¥ÂÂ¡Ã§Â«Â¯Ã©ÂÂÃ§Â½Â®
store.mode=db
store.db.datasource=druid
store.db.dbType=mysql
store.db.driverClassName=com.mysql.cj.jdbc.Driver
store.db.url=*************************************************************************************************************************************
store.db.user=root
store.db.password=123
store.db.minConn=5
store.db.maxConn=30
store.db.globalTable=global_table
store.db.branchTable=branch_table
store.db.lockTable=lock_table
store.db.queryLimit=100
store.db.maxWait=5000

# Ã¤ÂºÂÃ¥ÂÂ¡Ã¦ÂÂ¥Ã¥Â¿ÂÃ©ÂÂÃ§Â½Â®
log.exceptionRate=100

# Ã¤ÂºÂÃ¥ÂÂ¡Ã§Â»ÂÃ©ÂÂÃ§Â½Â®
service.vgroupMapping.order-service-group=default
service.vgroupMapping.inventory-service-group=default
service.vgroupMapping.account-service-group=default
service.enableDegrade=false
service.disableGlobalTransaction=false

# Ã¥Â®Â¢Ã¦ÂÂ·Ã§Â«Â¯Ã©ÂÂÃ§Â½Â®
client.rm.asyncCommitBufferLimit=10000
client.rm.lock.retryInterval=10
client.rm.lock.retryTimes=30
client.rm.lock.retryPolicyBranchRollbackOnConflict=true
client.rm.reportRetryCount=5
client.rm.tableMetaCheckEnable=false
client.rm.reportSuccessEnable=false
client.rm.sqlParserType=druid
client.tm.commitRetryCount=5
client.tm.rollbackRetryCount=5
client.undo.dataValidation=true
client.undo.logSerialization=jackson
client.undo.logTable=undo_log

# transportÃ©ÂÂÃ§Â½Â®
transport.type=TCP
transport.server=NIO
transport.heartbeat=true
transport.threadFactory.bossThreadPrefix=NettyBoss
transport.threadFactory.workerThreadPrefix=NettyServerNIOWorker
transport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler
transport.threadFactory.shareBossWorker=false
transport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector
transport.threadFactory.clientSelectorThreadSize=1
transport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread
transport.threadFactory.bossThreadSize=1
transport.threadFactory.workerThreadSize=default
transport.shutdown.wait=3
transport.serialization=seata
transport.compressor=none

# registryÃ©ÂÂÃ§Â½Â®
registry.type=nacos
registry.nacos.application=seata-server
registry.nacos.server-addr=nacos-server:8848
registry.nacos.group=SEATA_GROUP
registry.nacos.namespace=dev
registry.nacos.cluster=default

# configÃ©ÂÂÃ§Â½Â®
config.type=nacos
config.nacos.server-addr=nacos-server:8848
config.nacos.namespace=dev
config.nacos.group=SEATA_GROUP
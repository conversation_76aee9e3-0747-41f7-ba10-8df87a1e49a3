version: "3.9"
services:
  nacos-server:
    image: nacos/nacos-server:v2.5.0
    container_name: nacos-server
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    environment:
      - MODE=standalone
      - JVM_XMS=512m
      - JVM_XMX=512m
      - JVM_XMN=256m
    networks:
      - zhao-network
    volumes:
      - nacos-data:/home/<USER>/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

  seata-server:
    image: seataio/seata-server:1.8.0
    container_name: seata-server
    ports:
      - "8091:8091"
      - "7091:7091"
    environment:
      - SEATA_PORT=8091
      - TZ=Asia/Shanghai
      - SEATA_CONFIG_TYPE=nacos
      - SEATA_CONFIG_NACOS_SERVER_ADDR=nacos-server:8848
      - SEATA_CONFIG_NACOS_NAMESPACE=dev
      - SEATA_CONFIG_NACOS_GROUP=SEATA_GROUP
      - SEATA_CONFIG_NACOS_DATA_ID=seataServer.properties
      - SEATA_REGISTRY_TYPE=nacos
      - SEATA_REGISTRY_NACOS_SERVER_ADDR=nacos-server:8848
      - SEATA_REGISTRY_NACOS_NAMESPACE=dev
      - SEATA_REGISTRY_NACOS_GROUP=SEATA_GROUP
      - SEATA_REGISTRY_NACOS_CLUSTER=default
    expose:
      - 8091
      - 7091
    depends_on:
      - nacos-server
    networks:
      - zhao-network
    # 使用 Nacos 配置中心，无需本地挂载
    # volumes:
    #   - /Users/<USER>/Documents/Code/zhao-springcloud/seata-config:/seata-server/resources
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8091"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

  sentinel-dashboard:
    image: bladex/sentinel-dashboard:1.8.4
    container_name: sentinel-dashboard
    ports:
      - "8858:8858"
    networks:
      - zhao-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8858"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

  mysql:
    image: mysql:8.0
    container_name: mysql-8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123
      MYSQL_ROOT_HOST: "%"
    ports:
      - "3306:3306"
    networks:
      - zhao-network
    volumes:
      - mysql-data:/var/lib/mysql
    command:
      - --default-authentication-plugin=mysql_native_password
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --explicit_defaults_for_timestamp=true
      - --bind-address=0.0.0.0
      - --skip-name-resolve
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123"]
      interval: 10s
      timeout: 5s
      retries: 30
      start_period: 60s

  rocketmq-namesrv:
    image: apache/rocketmq:5.3.1
    container_name: rocketmq-namesrv
    platform: linux/arm64/v8
    ports:
      - "9876:9876"
    command: ["sh", "-c", "cd /home/<USER>/rocketmq-5.3.1/bin && ./mqnamesrv"]
    networks:
      - zhao-network
    healthcheck:
      test: ["CMD", "bash", "-lc", "</dev/tcp/127.0.0.1/9876"]
      interval: 10s
      timeout: 5s
      retries: 30
      start_period: 30s

  rocketmq-broker:
    image: apache/rocketmq:5.3.1
    container_name: rocketmq-broker
    platform: linux/arm64/v8
    depends_on:
      - rocketmq-namesrv
    ports:
      - "10911:10911"  # Broker listening port
      - "10909:10909"  # HA sync port
    command: ["sh", "-c", "echo 'brokerClusterName=DefaultCluster\nbrokerName=broker-a\nbrokerId=0\nlistenPort=10911\nnamesrvAddr=rocketmq-namesrv:9876\nautoCreateTopicEnable=true\nautoCreateSubscriptionGroup=true' > /home/<USER>/broker.conf && cd /home/<USER>/rocketmq-5.3.1/bin && ./mqbroker -c /home/<USER>/broker.conf"]
    environment:
      - JAVA_OPT_EXT=-Xms256m -Xmx256m -Xmn128m
    networks:
      - zhao-network
    healthcheck:
      test: ["CMD", "bash", "-lc", "</dev/tcp/127.0.0.1/10911"]
      interval: 10s
      timeout: 5s
      retries: 30
      start_period: 30s

  rocketmq-dashboard:
    image: apacherocketmq/rocketmq-dashboard:latest
    container_name: rocketmq-dashboard
    ports:
      - "8088:8080"
    environment:
      - JAVA_OPTS=-Drocketmq.namesrv.addr=rocketmq-namesrv:9876
    depends_on:
      - rocketmq-namesrv
    networks:
      - zhao-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 30s

networks:
  zhao-network:
    driver: bridge
    name: zhao-springcloud_zhao-network

volumes:
  nacos-data:
  mysql-data:
version: "3.9"
services:
  nacos-server:
    image: nacos/nacos-server:v2.5.0
    container_name: nacos-server
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    environment:
      - MODE=standalone
      - JVM_XMS=512m
      - JVM_XMX=512m
      - JVM_XMN=256m
    networks:
      - zhao-network
    volumes:
      - nacos-data:/home/<USER>/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

  seata-server:
    image: seataio/seata-server:1.8.0
    container_name: seata-server
    ports:
      - "8091:8091"
      - "7091:7091"
    environment:
      - SEATA_PORT=8091
      - TZ=Asia/Shanghai
    expose:
      - 8091
      - 7091
    depends_on:
      - nacos-server
    networks:
      - zhao-network
    volumes:
      - ./seata-config:/seata-server/resources
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8091"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

  sentinel-dashboard:
    image: bladex/sentinel-dashboard:1.8.4
    container_name: sentinel-dashboard
    ports:
      - "8858:8858"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8858"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

networks:
  zhao-network:
    driver: bridge
    name: zhao-springcloud_zhao-network

volumes:
  nacos-data:
  seata-config: